package model

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// Question 题目信息模型
type Question struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement;comment:主键ID"`
	CacheKeyHash  string    `json:"cache_key_hash" gorm:"type:varchar(255);not null;index;comment:被哈希化的缓存键名字"`
	QuestionType  string    `json:"question_type" gorm:"type:varchar(50);not null;index;comment:问题类型"`
	QuestionText  string    `json:"question_text" gorm:"type:text;not null;comment:问题内容"`
	OptionA       *string   `json:"option_a,omitempty" gorm:"type:text;comment:问题选项A"`
	OptionB       *string   `json:"option_b,omitempty" gorm:"type:text;comment:问题选项B"`
	OptionC       *string   `json:"option_c,omitempty" gorm:"type:text;comment:问题选项C"`
	OptionD       *string   `json:"option_d,omitempty" gorm:"type:text;comment:问题选项D"`
	OptionY       *string   `json:"option_y,omitempty" gorm:"type:text;comment:问题选项Y"`
	OptionN       *string   `json:"option_n,omitempty" gorm:"type:text;comment:问题选项N"`
	Answer        JSONField `json:"answer,omitempty" gorm:"type:json;comment:问题答案"`
	Analysis      *string   `json:"analysis,omitempty" gorm:"type:text;comment:问题解析"`
	UserImage     *string   `json:"user_image,omitempty" gorm:"type:varchar(500);comment:问题对应的图片名称"`
	ImageURL      string    `json:"image_url" gorm:"type:varchar(1000);not null;comment:用户提交的图片URL地址"`
	QwenRaw       JSONField `json:"qwen_raw,omitempty" gorm:"type:json;comment:Qwen返回的原始数据"`
	DeepseekRaw   JSONField `json:"deepseek_raw,omitempty" gorm:"type:json;comment:DeepSeek返回的原始数据"`
	QwenParsed    JSONField `json:"qwen_parsed,omitempty" gorm:"type:json;comment:被格式化解析后的Qwen数据"`
	IsVerified    int       `json:"is_verified" gorm:"type:tinyint;default:0;comment:是否已经验证过"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// JSONField 自定义JSON字段类型
type JSONField map[string]interface{}

// Value 实现driver.Valuer接口
func (j JSONField) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONField) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, j)
}

// QuestionResponse 返回给用户的题目数据结构
type QuestionResponse struct {
	QuestionType string                 `json:"question_type"`
	QuestionText string                 `json:"question_text"`
	Options      map[string]string      `json:"options"`
	Answer       map[string]string      `json:"answer"`
	Analysis     string                 `json:"analysis"`
	ImageURL     string                 `json:"image_url"`
	UserImage    string                 `json:"user_image"`
	IsVerified   string                 `json:"is_verified"`
}

// QwenData Qwen返回的数据结构
type QwenData struct {
	QuestionType string            `json:"question_type"`
	QuestionText string            `json:"question_text"`
	QuestionNum  string            `json:"question_num,omitempty"`
	Options      map[string]string `json:"options"`
}

// DeepseekData DeepSeek返回的数据结构
type DeepseekData struct {
	QuestionType string                 `json:"question_type"`
	QuestionText string                 `json:"question_text"`
	Options      map[string]string      `json:"options"`
	Answer       map[string]string      `json:"answer"`
	Analysis     string                 `json:"analysis"`
	ImageURL     string                 `json:"image_url"`
	UserImage    string                 `json:"user_image"`
	IsVerified   string                 `json:"is_verified"`
}
