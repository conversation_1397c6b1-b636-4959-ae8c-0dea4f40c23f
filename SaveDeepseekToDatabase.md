SaveDeepseekToDatabase方法业务逻辑介绍：

预期中deepseek返回的数据格式中的content字段内包含以下内容

{
  "question_type": "多选题",
  "question_text": "雾天跟车行驶,应如何安全驾驶?",
  "options": {
    "A": "加大跟车距离,降低行驶速度",
    "B": "提前开启雾灯、危险报警闪光灯",
    "C": "以前车尾灯作为判断安全距离的参照物",
    "D": "按喇叭提示行车位置"
  },
  "answer": {
    "A": "加大跟车距离,降低行驶速度",
    "B": "提前开启雾灯、危险报警闪光灯",
    "C": "以前车尾灯作为判断安全距离的参照物"
  },
  "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。", 
  "image_url": "https://www.qdq.cc.qdqdq.png", 
  "user_image": "https://www.qdq.cc.qdqdq.png",   
  "is_verified": "0"
}


得到deepseek返回数据后，我们需要将得到的数据分别存入数据库

在返回数据前，已经获得的值有
cache_key_hash  = 被哈希化的缓存键名字	
question_type  =  问题类型	
question_text  =  问题内容	
option_a  =  问题选项A
option_b  =  问题选项B
option_c  =  问题选项C
option_d  =  问题选项D	
option_y  =  问题选项Y	
option_n  =  问题选项N	
user_image  =  问题对应的图片名称	
image_url  =  用户提交的图片 URL 地址
qwen_raw  =  qwen 返回的原始数据	
qwen_parsed  =  被格式化解析后的 qwen 数据	
is_verified  =  是否已经验证过，默认值为0

在得到deepseek返回数据后，我们又得到的值为
deepseek_raw  =  deepseek 返回的原始数据
answer  =  问题答案	
analysis  =  问题解析	



这里需要注意answer是一个json，需要将这个json原样存入字段answer中。


将上述值存入mysql后最终完整入库。然后回写redis

回写redis时从mysql取的值不应是所有字段，因为mysql的所有字段中包含了敏感信息，不应该暴露给用户。

最终给用户返回的值应该按如下示例的字段返回


[
  {
    "question_type": "多选题",
    "question_text": "雾天跟车行驶,应如何安全驾驶?",
    "options": {
      "A": "加大跟车距离,降低行驶速度",
      "B": "提前开启雾灯、危险报警闪光灯",
      "C": "以前车尾灯作为判断安全距离的参照物",
      "D": "按喇叭提示行车位置"
    },
    "answer": {
      "A": "加大跟车距离,降低行驶速度",
      "B": "提前开启雾灯、危险报警闪光灯",
      "C": "以前车尾灯作为判断安全距离的参照物"
    },
    "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。", 
  "image_url": "https://www.qdq.cc.qdqdq.png", 
  "user_image": "https://www.qdq.cc.qdqdq.png",   
  "is_verified": "0"
  }
]

另外需要注意回写逻辑，先检查mysql中是否存在cache_key_hash值相同的记录，如果存在需要将相同cache_key_hash的记录一起回写；


假设三个问题的cache_key_hash相同，则将三个问题按照以下格式全部作为valve回写redis（虽然不成立，但业务逻辑要求如此处理，因为在特殊情况下管理员会手动更改mysql记录的cache_key_hash。以保证特殊场景使用。）


[
  {
    "question_type": "多选题",
    "question_text": "雾天跟车行驶,应如何安全驾驶?",
    "options": {
      "A": "加大跟车距离,降低行驶速度",
      "B": "提前开启雾灯、危险报警闪光灯",
      "C": "以前车尾灯作为判断安全距离的参照物",
      "D": "按喇叭提示行车位置"
    },
    "answer": {
      "A": "加大跟车距离,降低行驶速度",
      "B": "提前开启雾灯、危险报警闪光灯",
      "C": "以前车尾灯作为判断安全距离的参照物"
    },
    "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因为声音在雾中传播不如光信号有效，因此不是最佳选择。", 
  "image_url": "https://www.qdq.cc.qdqdq.png", 
  "user_image": "https://www.qdq.cc.qdqdq.png",   
  "is_verified": "0"
  },
  {
    "question_type": "判断题",
    "question_text": "驾驶机动车在雾天应使用远光灯提高能见度。",
    "options": {
      "A": "正确",
      "B": "错误"
    },
    "answer": {
      "B": "错误"
    },
    "analysis": "雾天使用远光灯会导致光线反射，降低能见度，正确做法是使用雾灯，因此本题错误。"
  },
  {
    "question_type": "单选题",
    "question_text": "在高速公路上发生故障或事故，应如何设置警告标志？",
    "options": {
      "A": "在车后50米处设置",
      "B": "在车后100米处设置",
      "C": "在车后150米外设置",
      "D": "在车后200米外设置"
    },
    "answer": {
      "C": "在车后150米外设置"
    },
    "analysis": "在高速公路上，应在车辆后方150米以外设置警告标志，以提示后车注意减速，避免发生二次事故。", 
  "image_url": "https://www.qdq.cc.qdqdq.png", 
  "user_image": "https://www.qdq.cc.qdqdq.png",   
  "is_verified": "0"
  }
]

