package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"

	"go-api-solve/internal/model"
)

// writeToLogFile 将日志写入文件
func writeToLogFile(logType, data string) {
	// 创建logs目录（如果不存在）
	if err := os.MkdirAll("logs", 0755); err != nil {
		log.Printf("Failed to create logs directory: %v", err)
		return
	}

	// 打开或创建日志文件
	file, err := os.OpenFile("logs/deepseek_requests.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.Printf("Failed to open log file: %v", err)
		return
	}
	defer file.Close()

	// 写入日志
	timestamp := time.Now().Format("2006/01/02 15:04:05")
	logEntry := fmt.Sprintf("[%s] === %s ===\n%s\n=== End %s ===\n\n", timestamp, logType, data, logType)

	if _, err := file.WriteString(logEntry); err != nil {
		log.Printf("Failed to write to log file: %v", err)
	}
}

// DeepseekClient DeepSeek API客户端
type DeepseekClient struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// NewDeepseekClient 创建新的DeepSeek客户端
func NewDeepseekClient(apiKey string) *DeepseekClient {
	return &DeepseekClient{
		APIKey:  apiKey,
		BaseURL: "https://api.deepseek.com/chat/completions",
		Client: &http.Client{
			Timeout: 60 * time.Second,
		},
	}
}

// CallDeepseekChat 调用DeepSeek-Chat模型
func (c *DeepseekClient) CallDeepseekChat(ctx context.Context, qwenParsedData string, config *model.ModelConfig) (*model.DeepseekResponse, error) {
	// 构建请求体
	requestBody := model.DeepseekRequest{
		Messages: []model.DeepseekMessage{
			{
				Content: config.RoleSystem,
				Role:    "system",
			},
			{
				Content: config.RoleUser,
				Role:    "user",
			},
			{
				Content: qwenParsedData,
				Role:    "user",
			},
		},
		Model:            "deepseek-chat",
		FrequencyPenalty: config.RepetitionPenalty,
		PresencePenalty:  config.PresencePenalty,
		ResponseFormat: model.ResponseFormat{
			Type: config.ResponseFormat,
		},
		Temperature: config.Temperature,
		TopP:        config.TopP,
	}

	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	// 记录发送给DeepSeek的完整原始数据
	log.Printf("=== DeepSeek Request Raw Data ===")
	log.Printf("%s", string(jsonData))
	log.Printf("=== End DeepSeek Request Raw Data ===")

	// 同时写入到文件日志
	writeToLogFile("DeepSeek Request", string(jsonData))

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", c.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIKey)

	// 发送请求
	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// 解析响应
	var deepseekResponse model.DeepseekResponse
	if err := json.Unmarshal(bodyBytes, &deepseekResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return &deepseekResponse, nil
}

// ExtractContentFromDeepseekResponse 从DeepSeek响应中提取内容
func ExtractContentFromDeepseekResponse(response *model.DeepseekResponse) (string, error) {
	// 检查choices是否存在
	if len(response.Choices) == 0 {
		return "", fmt.Errorf("no choices found in DeepSeek response")
	}

	// 返回第一个choice的内容
	return response.Choices[0].Message.Content, nil
}
