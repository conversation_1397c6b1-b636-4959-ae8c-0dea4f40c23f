{"summary": {"failed_requests": 100, "success_rate": 0, "success_requests": 0, "test_duration": "8.712417ms", "timeout_requests": 0, "total_requests": 100}, "test_config": {"BaseURL": "http://localhost:8080", "ImageBaseURL": "http://solve.igmdns.com/img/", "StartImage": 1, "EndImage": 100, "Interval": 15000000000, "Timeout": 30000000000, "ThreadCount": 10, "RoundCount": 1}, "test_stats": {"TotalRequests": 100, "SuccessRequests": 0, "FailedRequests": 100, "TimeoutRequests": 0, "StartTime": "2025-06-10T10:40:46.384564+08:00", "EndTime": "2025-06-10T10:40:46.393276+08:00", "ResponseTimes": [2929542, 2949000, 1598625, 2952292, 2989375, 2995958, 1604959, 2934666, 2918833, 2922792, 324584, 219000, 191709, 347667, 416958, 383250, 386458, 407041, 389917, 512000, 400417, 335083, 510000, 442500, 427125, 674875, 515458, 477125, 574375, 665792, 301250, 282417, 331959, 371750, 222709, 347583, 348333, 380667, 377292, 408417, 494375, 374125, 300917, 532291, 340958, 517417, 410292, 290375, 423708, 411458, 114000, 424167, 441917, 320458, 312125, 290333, 257750, 392708, 330708, 274666, 253041, 415875, 318750, 281833, 253209, 334583, 279084, 289083, 190958, 242958, 253250, 258875, 369375, 175750, 335917, 423125, 296917, 294708, 460833, 392000, 233667, 292458, 393209, 160709, 286708, 192833, 194625, 201708, 215875, 334791, 266292, 231750, 115250, 154125, 160000, 284583, 111292, 227500, 219000, 255875], "Errors": ["线程10-图片91.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片21.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片71.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片11.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片81.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片51.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片31.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片01.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片61.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片41.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片52.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片62.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片42.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片02.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片72.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片82.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片22.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片92.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片32.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片12.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片43.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片33.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片53.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片83.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片93.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片13.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片73.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片23.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片03.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片63.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片54.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片84.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片34.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片44.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片85.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片14.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片04.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片74.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片24.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片64.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片94.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片55.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片05.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片35.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片75.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片45.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片86.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片65.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片15.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片25.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片16.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片56.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片95.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片06.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片36.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片87.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片66.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片76.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片26.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片17.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片57.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片46.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片96.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片37.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片88.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片07.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片67.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片77.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片97.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片18.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片27.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片47.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片58.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片28.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片08.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片38.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片98.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片19.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片89.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片68.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片59.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片48.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片78.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片49.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片09.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程9-图片90.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片69.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片99.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程6-图片60.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片29.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片39.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程2-图片20.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程7-图片70.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程5-图片50.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程1-图片10.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片79.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程8-图片80.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程3-图片30.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程4-图片40.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused", "线程10-图片100.jpg: 请求失败: Post \"http://localhost:8080/api/v1/process-image\": dial tcp [::1]:8080: connect: connection refused"]}}