本文档仅用于理解qwen-vl-plus模型的调用方式以及参数配置。更多的结构参数请参考qwen-vl-plus的官方文档。
禁止移除目前文档要求的字段的配置项，这些是必须参在的且可配置的，如果需要增加其他值，需要与我沟通确认。

需要一个独立的数据表quest_model_config来存储模型的配置参数。

表所需字段//支持数据库配置
model_name  =  模型名称  
role_system  =  system角色的Content
role_user  =  user角色的Content 
temperature  =  温度 //默认值为0
top_p  =  TopP  //默认值为0.8 越高文本越多样性
top_k  =  TopK  //默认值为50  候选集越少，稳定稳定性越高
repetition_penalty  =  重复惩罚  //OCR建议为1-1.05
presence_penalty  =  存在惩罚   //OCR建议1.5
response_format  =  返回格式  //json_object或text


qwen-vl-plus调用方法以及定制化需求；DashScope方法调用；按照以下格式请求，要求的参数需要数据库来配置值。


Model: "qwen-vl-plus",
		Input: Input{
			Messages: []Message{
				{
					Role:    "system",
					Content: "你是一个*****", //这里由数据库配置读取role_system
				},
				{
					Role:    "user",
                    image：     "image_url",  //这里的image_url是用户提交的图片地址，不需要后台配置。
					Content: "你是一个*****",   //这里由数据库配置读取role_user
				},
			},
		},
		Parameters: Parameters{
            temperature : 0.2,  //这里由数据库配置读取temperature
			TopP: 0.8,      //这里由数据库配置读取top_p
			TopK: 50,       //这里由数据库配置读取top_k
            repetition_penalty      //这里由数据库配置读取repetition_penalty
            presence_penalty        //这里由数据库配置读取presence_penalty
            response_format         //这里由数据库配置读取response_format
		},
	}

	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		log.Fatal(err)
	}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation", bytes.NewBuffer(jsonData))
	if err != nil {
		log.Fatal(err)
	}

	// 设置请求头
	// 若没有配置环境变量，请用百炼API Key将下行替换为：apiKey := "sk-xxx"
	apiKey := os.Getenv("DASHSCOPE_API_KEY")
	req.Header.Set("Authorization", "Bearer "+apiKey)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal(err)
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyText, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatal(err)
	}

	// 打印响应内容
	fmt.Printf("%s\n", bodyText)


