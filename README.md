# Go API Solve - 图片题目解析服务

基于 Go + Gin + MySQL8 + Redis 的 API 服务，用于处理图片题目解析和智能问答。

## 项目概述

本项目是一个图片题目解析服务，用户提交图片URL，系统通过AI模型（Qwen-VL-Plus 和 DeepSeek）进行图片解析和题目处理，并提供缓存机制以提高响应速度。

## 技术栈

- **后端框架**: Gin (Go)
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **AI模型**: Qwen-VL-Plus, DeepSeek-Chat

## 项目结构

```
Go_api_solve/
├── cmd/
│   └── server/
│       └── main.go                 # 应用入口
├── internal/
│   ├── config/                     # 配置管理
│   │   ├── config.go
│   │   └── database.go
│   ├── handler/                    # HTTP处理器
│   │   └── question.go
│   ├── service/                    # 业务逻辑层
│   │   ├── question.go
│   │   ├── qwen.go
│   │   └── deepseek.go
│   ├── repository/                 # 数据访问层
│   │   ├── question.go
│   │   └── model_config.go
│   ├── model/                      # 数据模型
│   │   ├── question.go
│   │   └── model_config.go
│   ├── middleware/                 # 中间件
│   │   └── cors.go
│   └── utils/                      # 工具函数
│       ├── hash.go
│       ├── image.go
│       └── format.go
├── pkg/
│   ├── database/                   # 数据库连接
│   │   └── mysql.go
│   ├── redis/                      # Redis连接
│   │   └── redis.go
│   └── ai/                         # AI模型调用
│       ├── qwen.go
│       └── deepseek.go
├── migrations/                     # 数据库迁移文件
│   ├── 001_create_questions_table.sql
│   └── 002_create_model_config_table.sql
├── docs/                          # 文档
├── go.mod
├── go.sum
└── README.md
```

## 核心功能

### 业务流程

1. **图片验证**: 验证用户提交的图片URL是否有效可访问
2. **Qwen模型调用**: 将图片提交给Qwen-VL-Plus模型进行初步解析
3. **数据格式化**: 使用`FormatQwenData`方法格式化Qwen返回的数据
4. **缓存查询**: 生成哈希缓存键，查询Redis缓存
5. **数据库查询**: 如Redis无缓存，则查询MySQL数据库
6. **DeepSeek调用**: 如数据库无数据，则调用DeepSeek模型进行深度解析
7. **数据存储**: 使用`SaveDeepseekToDatabase`方法将数据存储到数据库
8. **缓存回写**: 使用`WriteToRedis`方法将数据写入Redis缓存
9. **结果返回**: 返回格式化的题目数据给用户

### 核心方法

- `FormatQwenData`: 格式化Qwen返回的数据，处理题目类型和选项
- `WriteToRedis`: 将数据回写到Redis缓存
- `SaveDeepseekToDatabase`: 保存DeepSeek返回的数据到数据库

## 数据库设计

### 主表 (questions)
存储题目的完整信息，包括原始数据和解析结果。

### 配置表 (quest_model_config)
存储AI模型的配置参数，支持动态配置。

## 开发进度

- [x] 项目结构设计
- [x] 数据库设计和迁移
- [x] 配置管理模块
- [x] 数据库连接模块
- [x] Redis连接模块
- [x] AI模型调用模块
- [x] 核心业务逻辑
- [x] HTTP路由和处理器
- [x] 依赖安装和测试
- [x] 项目完成和文档

## 已完成的模块

### 1. 数据库设计 ✅
- **主表 (questions)**: 存储题目完整信息，包括原始数据和解析结果
- **配置表 (quest_model_config)**: 存储AI模型配置参数，支持动态配置
- **自动迁移**: 支持GORM自动迁移和SQL迁移文件

### 2. 配置管理 ✅
- **环境变量支持**: 支持从环境变量读取配置
- **默认配置**: 提供生产环境的默认配置值
- **结构化配置**: 分模块管理服务器、数据库、Redis、AI配置

### 3. 数据访问层 ✅
- **Repository模式**: 实现数据访问层抽象
- **GORM集成**: 使用GORM进行数据库操作
- **错误处理**: 完善的错误处理和日志记录

### 4. 业务逻辑层 ✅
- **模块化设计**: 分离Qwen、DeepSeek、Question服务
- **核心方法实现**:
  - `FormatQwenData`: 格式化Qwen返回数据，支持题干清洗
  - `WriteToRedis`: 回写Redis缓存，支持多题目场景
  - `SaveDeepseekToDatabase`: 保存DeepSeek数据到数据库
- **完整业务流程**: 实现图片验证→Qwen调用→缓存查询→数据库查询→DeepSeek调用→数据存储→缓存回写

### 5. AI模型集成 ✅
- **Qwen-VL-Plus**: 图片识别和初步解析
- **DeepSeek-Chat**: 深度分析和答案生成
- **动态配置**: 支持从数据库动态读取模型参数
- **错误处理**: 完善的API调用错误处理

### 6. 缓存系统 ✅
- **Redis集成**: 使用go-redis客户端
- **缓存策略**: 基于完整数据哈希的缓存键生成
- **过期管理**: 24小时缓存过期时间

### 7. HTTP接口 ✅
- **RESTful API**: 标准的REST接口设计
- **统一响应**: 标准化的API响应格式
- **错误处理**: 分类处理不同类型的错误
- **中间件**: CORS、日志、恢复中间件

## 技术实现要点

### 缓存策略
- 使用完整的格式化数据进行哈希生成缓存键
- 缓存键格式: `quest:{hash_value}`
- 支持多题目同缓存键的特殊业务场景

### AI模型集成
- 支持动态配置模型参数
- Qwen-VL-Plus用于图片识别和初步解析
- DeepSeek-Chat用于深度分析和答案生成

### 数据处理
- 题目类型支持：单选题、多选题、判断题
- 智能清洗题干中的序号和标记
- JSON格式的选项和答案存储

## API接口文档

### 1. 处理图片题目
**接口地址**: `POST /api/v1/process-image`

**请求参数**:
```json
{
  "image_url": "http://solve.igmdns.com/img/24.jpg"
}
```

**成功响应**:
```json
{
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "question_type": "多选题",
      "question_text": "雾天跟车行驶,应如何安全驾驶?",
      "options": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物",
        "D": "按喇叭提示行车位置"
      },
      "answer": {
        "A": "加大跟车距离,降低行驶速度",
        "B": "提前开启雾灯、危险报警闪光灯",
        "C": "以前车尾灯作为判断安全距离的参照物"
      },
      "analysis": "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度...",
      "image_url": "http://solve.igmdns.com/img/24.jpg",
      "user_image": "24.jpg",
      "is_verified": "0"
    }
  ]
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "图片资源不存在，请重新上传"
}
```

### 2. 健康检查
**接口地址**: `GET /api/v1/health`

**响应**:
```json
{
  "code": 200,
  "message": "服务正常运行",
  "data": {
    "status": "healthy",
    "service": "go-api-solve"
  }
}
```

## 快速开始

### 1. 环境要求
- Go 1.21+
- MySQL 8.0+
- Redis 6.0+

### 2. 安装依赖
```bash
go mod tidy
```

### 3. 配置环境变量（可选）
```bash
export MYSQL_HOST=***********
export MYSQL_PORT=3306
export MYSQL_USERNAME=gmdns
export MYSQL_PASSWORD=5e7fFn3HpPfuQ6Qx42Az
export MYSQL_DATABASE=solve_api_go
export REDIS_HOST=r-bp1t323p6w8yn2cpq0pd.redis.rds.aliyuncs.com
export REDIS_PORT=6379
export REDIS_USERNAME=r-bp1t323p6w8yn2cpq0
export REDIS_PASSWORD=SsYZyxSSr8uEVWKJ
export QWEN_KEY=sk-3920274bedf642c2b7495f534aadca84
export DEEPSEEK_KEY=***********************************
```

### 4. 运行服务
```bash
go run cmd/server/main.go
```

### 5. 测试接口
```bash
curl -X POST http://localhost:8080/api/v1/process-image \
  -H "Content-Type: application/json" \
  -d '{"image_url": "http://solve.igmdns.com/img/24.jpg"}'
```

## 项目特色

### 🚀 高性能架构
- **多级缓存**: Redis + MySQL 双重缓存策略
- **智能降级**: 缓存未命中时自动降级到数据库查询
- **异步处理**: 支持高并发请求处理

### 🧠 AI 智能处理
- **双模型协作**: Qwen-VL-Plus 负责图片识别，DeepSeek-Chat 负责深度分析
- **动态配置**: 支持从数据库动态调整AI模型参数
- **智能解析**: 自动清洗题干，支持多种题目格式

### 🔧 模块化设计
- **分层架构**: Handler -> Service -> Repository 清晰分层
- **依赖注入**: 松耦合的模块设计，便于测试和扩展
- **接口抽象**: 易于替换和扩展不同的实现

### 📊 完善的数据处理
- **题目类型支持**: 单选题、多选题、判断题
- **数据格式化**: JSON格式存储，便于查询和处理
- **完整性保证**: 原始数据和处理结果都完整保存

## 文档和测试

### 📚 完整文档
- [部署指南](docs/DEPLOYMENT.md) - 详细的部署和配置说明
- [API测试文档](docs/API_TEST.md) - 完整的API测试用例和脚本
- [数据库迁移文件](migrations/) - 标准的数据库结构定义

### 🧪 测试覆盖
- **单元测试**: 核心功能单元测试
- **集成测试**: API接口集成测试
- **性能测试**: 并发和缓存性能测试
- **错误处理测试**: 各种异常情况的处理测试

## 技术亮点

### 1. 智能缓存策略
```
用户请求 -> Redis缓存 -> MySQL数据库 -> AI模型处理
     ↓         ↓           ↓            ↓
   直接返回   缓存命中    数据库命中    新数据处理
```

### 2. 数据一致性保证
- 使用完整数据哈希确保缓存键的唯一性
- 支持多题目共享缓存键的特殊业务场景
- 原子性的数据库操作保证数据完整性

### 3. 错误处理机制
- 分层错误处理，每层都有相应的错误处理逻辑
- 用户友好的错误信息返回
- 完整的日志记录便于问题排查

### 4. 可扩展性设计
- 模块化的服务设计，易于添加新的AI模型
- 配置化的参数管理，支持动态调整
- 标准的REST API设计，便于前端集成

## 开发总结

本项目严格按照需求文档进行开发，实现了以下核心功能：

✅ **完整的业务流程**: 图片验证 → Qwen识别 → 数据格式化 → 缓存查询 → 数据库查询 → DeepSeek分析 → 数据存储 → 结果返回

✅ **核心方法实现**:
- `FormatQwenData`: 格式化Qwen数据，支持题干清洗和类型验证
- `WriteToRedis`: 智能缓存回写，支持多题目场景
- `SaveDeepseekToDatabase`: 完整的数据存储和关联查询

✅ **模块化架构**: 采用标准的Go项目结构，分层清晰，便于维护和扩展

✅ **完善的文档**: 提供了详细的部署指南、API测试文档和技术实现说明

✅ **生产就绪**: 包含了完整的错误处理、日志记录、性能优化和部署配置

项目已经完成了所有核心功能的开发，可以直接用于生产环境部署和使用。
